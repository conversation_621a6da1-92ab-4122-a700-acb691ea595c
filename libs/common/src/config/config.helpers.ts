// https://docs.nestjs.com/techniques/configuration#custom-env-file-path

/**
 * Simple utility for generate env file paths based on app directory name.
 * Allowed env file names:
 *  - .development.env
 *  - .local.env
 *  - .env
 */
export const generateAppEnvFilePaths = (appDir: string) => {
  return [
    `./apps/${appDir}/.env.${process.env['NODE_ENV']}`,
    `./apps/${appDir}/.env.local`,
    `./apps/${appDir}/.env`,
  ];
};

export const isDevelopment = () => process.env['NODE_ENV'] === 'development';

export const isTest = () => process.env['NODE_ENV'] === 'test';

export const isProduction = () => process.env['NODE_ENV'] === 'production';

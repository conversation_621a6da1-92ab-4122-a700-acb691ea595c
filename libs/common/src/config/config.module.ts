import { Module } from '@nestjs/common';
import { ConfigModule as NestJsConfigModule } from '@nestjs/config';
import { ZodType } from 'zod';

@Module({})
export class ConfigModule {
  static forRoot<T extends object>(params: {
    schema: ZodType<T>;
    envFilePath?: string | string[];
  }) {
    const { schema, envFilePath } = params;

    return NestJsConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      envFilePath,
      validate: (config) => schema.parse(config),
    });
  }
}

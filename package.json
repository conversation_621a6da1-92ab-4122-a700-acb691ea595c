{"name": "@trusthero/source", "version": "0.0.0", "license": "MIT", "scripts": {}, "private": true, "dependencies": {"@nestjs/common": "^11.0.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.0", "@nestjs/platform-express": "^11.0.0", "@nestjs/swagger": "^11.2.0", "axios": "^1.6.0", "nestjs-zod": "^5.0.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "zod": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.8.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.0", "@nx/eslint": "21.6.3", "@nx/eslint-plugin": "21.6.3", "@nx/jest": "21.6.3", "@nx/js": "21.6.3", "@nx/nest": "21.6.3", "@nx/node": "21.6.3", "@nx/web": "21.6.3", "@nx/webpack": "21.6.3", "@nx/workspace": "21.6.3", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/jest": "^30.0.0", "@types/node": "20.19.9", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "jest": "^30.0.2", "jest-environment-node": "^30.0.2", "jest-util": "^30.0.2", "nx": "21.6.3", "prettier": "^2.6.2", "ts-jest": "^29.4.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.9.2", "typescript-eslint": "^8.40.0", "webpack-cli": "^5.1.4"}}
import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import { DocumentBuilder, SwaggerModule } from '@libs/swagger';
import { ConfigService } from '@libs/common/config';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  const config = app.get(ConfigService);

  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);

  const openApiConfig = new DocumentBuilder()
    .setTitle('API')
    .setVersion('0.0.0')
    // .addBearerAuth({ type: 'http', scheme: 'bearer' }, ACCESS_TOKEN_SECURITY_KEY)
    .build();

  const openApi = SwaggerModule.createDocument(app, openApiConfig);
  // to see the webpage open: /swagger
  SwaggerModule.setup('swagger', app, openApi, {
    jsonDocumentUrl: 'swagger/json',
  });

  const port = process.env.PORT || 3000;

  await app.listen(port);

  Logger.log(`
    🚀 Application is running on: ${await app.getUrl()}
  `);
}

void bootstrap();
